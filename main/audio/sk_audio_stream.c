/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.c
 * @description: WebSocket音频流处理实现
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2025-07-29
 */

// 标准库头文件
#include <string.h>
#include <stdlib.h>
#include <stdatomic.h>

// ESP-IDF头文件
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
// 移除 semphr.h - 不再使用信号量

// 项目基础头文件（按依赖层次排序）
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_audio_stream.h"
#include "sk_websocket.h"
#include "sk_audio_buffer.h"
#include "sk_opus_dec.h"

#define TAG "AudioStream"

// 配置参数
#define AUDIO_STREAM_BUFFER_COUNT   16      // 音频缓冲区数量
#define AUDIO_STREAM_FRAME_SIZE     960     // 16kHz, 60ms帧大小
#define AUDIO_STREAM_TIMEOUT_MS     100     // 超时时间
#define AUDIO_STREAM_MAX_SEQ_GAP    10      // 最大序列号间隔

// 简化的音频流控制结构体 - 无锁设计
typedef struct {
    volatile int state;                     // 简单状态变量（IDLE=0, STREAMING=1）
    atomic_uint expectedSeq;                // 原子序列号
    atomic_uint totalFrames;                // 原子统计：总帧数
    atomic_uint lostFrames;                 // 原子统计：丢失帧数
    atomic_uint bufferUnderrun;             // 原子统计：缓冲区欠载

    // 音频处理相关
    SkOpusDecHandler opusDecoder;           // Opus解码器句柄

    // 移除了互斥锁和复杂的统计结构体
} SkAudioStreamCtrl_t;

// 全局控制结构体
static SkAudioStreamCtrl_t g_audioStreamCtrl = {0};

// 内部函数声明
static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl,
                                         SkWsBinaryHeader_t *header,
                                         uint16_t totalLen);
static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen);

int32_t SkAudioStreamInit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    // 检查是否已初始化
    if (ctrl->opusDecoder != NULL) {
        SK_LOGI(TAG, "Audio stream already initialized");
        return SK_RET_SUCCESS;
    }

    // 获取现有的Opus解码器句柄
    ctrl->opusDecoder = SkOpusDecGetHandler();
    if (ctrl->opusDecoder == NULL) {
        SK_LOGE(TAG, "Failed to get Opus decoder handler");
        return SK_RET_FAIL;
    }

    // 初始化状态（原子操作）
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;
    atomic_store(&ctrl->expectedSeq, 0);
    atomic_store(&ctrl->totalFrames, 0);
    atomic_store(&ctrl->lostFrames, 0);
    atomic_store(&ctrl->bufferUnderrun, 0);

    SK_LOGI(TAG, "Audio stream initialized successfully (lock-free)");
    return SK_RET_SUCCESS;
}

void SkAudioStreamDeinit() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        return;  // 未初始化
    }

    // 停止流
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;

    // 清理资源 - 不需要释放Opus解码器，它是全局共享的
    ctrl->opusDecoder = NULL;

    SK_LOGI(TAG, "Audio stream deinitialized (lock-free)");
}

void SkAudioStreamOnBinaryData(void *arg, void *data, uint16_t len) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;
    SkWsBinaryHeader_t *header = (SkWsBinaryHeader_t *)data;

    // 检查初始化状态
    if (ctrl->opusDecoder == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return;
    }

    // 检查数据长度
    if (len < sizeof(SkWsBinaryHeader_t)) {
        SK_LOGE(TAG, "Invalid data length: %d", len);
        return;
    }

    // 检查协议版本
    if (header->version != SK_WS_VERSION) {
        SK_LOGE(TAG, "Invalid version: 0x%02x", header->version);
        return;
    }

    // 无锁处理 - 根据数据类型直接处理
    switch (header->type) {
        case SK_WS_DATA_TYPE_AUDIO:
            SkAudioStreamProcessAudioData(ctrl, header, len);
            break;

        case SK_WS_DATA_TYPE_AUDIO_CONTROL:
            SkAudioStreamProcessControlData(ctrl, header, len);
            break;

        default:
            SK_LOGD(TAG, "Unknown data type: 0x%02x", header->type);
            break;
    }
}

static void SkAudioStreamProcessAudioData(SkAudioStreamCtrl_t *ctrl,
                                         SkWsBinaryHeader_t *header,
                                         uint16_t totalLen) {
    // 检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGI(TAG, "Not streaming (state=%d), ignore audio data seq=%d",
                ctrl->state, header->seqNum);
        return;
    }

    SK_LOGI(TAG, "Processing audio data: seq=%d, len=%d, state=%d",
            header->seqNum, header->payloadLen, ctrl->state);
    
    uint16_t payloadLen = header->payloadLen;
    uint8_t *opusData = header->data;
    
    // 检查数据完整性
    if (totalLen < sizeof(SkWsBinaryHeader_t) + payloadLen) {
        SK_LOGE(TAG, "Incomplete audio data");
        atomic_fetch_add(&ctrl->lostFrames, 1);
        return;
    }

    // 简化序列号处理 - 不需要严格验证，Opus解码器会处理
    atomic_store(&ctrl->expectedSeq, header->seqNum + 1);

    // 使用现有的Opus解码器封装直接播放（不重置解码器）
    int32_t result = SkOpusDecPlayRemote(
        ctrl->opusDecoder,
        header->seqNum,
        opusData,
        payloadLen,
        NULL
    );

    // 原子更新统计信息
    if (result == SK_RET_SUCCESS) {
        uint32_t totalFrames = atomic_fetch_add(&ctrl->totalFrames, 1) + 1;
        SK_LOGI(TAG, "Opus decode success: seq=%d, size=%d, total_frames=%d",
                header->seqNum, payloadLen, totalFrames);
    } else {
        uint32_t lostFrames = atomic_fetch_add(&ctrl->lostFrames, 1) + 1;
        SK_LOGE(TAG, "Opus decode failed: seq=%d, result=%d, lost_frames=%d",
                header->seqNum, result, lostFrames);
    }
}

static void SkAudioStreamProcessControlData(SkAudioStreamCtrl_t *ctrl,
                                           SkWsBinaryHeader_t *header,
                                           uint16_t totalLen) {
    if (header->payloadLen < 1) {
        SK_LOGE(TAG, "Invalid control data length");
        return;
    }
    
    uint8_t cmd = header->data[0];

    SK_LOGI(TAG, "Received control command: 0x%02x (current_state=%d)",
            cmd, ctrl->state);
    
    // 无锁控制命令处理
    switch (cmd) {
        case SK_WS_AUDIO_CMD_START:
            SK_LOGI(TAG, "Processing START command");
            ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;  // 简单赋值
            atomic_store(&ctrl->expectedSeq, 0);            // 重置序列号
            atomic_store(&ctrl->totalFrames, 0);            // 重置统计
            // 不重置Opus解码器 - 让它连续工作
            SK_LOGI(TAG, "START command processed, new state=%d", ctrl->state);
            break;

        case SK_WS_AUDIO_CMD_STOP:
            SK_LOGI(TAG, "Processing STOP command");
            ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;       // 简单赋值
            // 输出最终统计
            uint32_t totalFrames = atomic_load(&ctrl->totalFrames);
            uint32_t lostFrames = atomic_load(&ctrl->lostFrames);
            SK_LOGI(TAG, "Audio streaming stopped - state=%d, total_frames=%d, lost_frames=%d",
                    ctrl->state, totalFrames, lostFrames);
            SK_LOGI(TAG, "STOP command processed, new state=%d", ctrl->state);
            break;

        case SK_WS_AUDIO_CMD_PAUSE:
            SK_LOGI(TAG, "Processing PAUSE command (not implemented)");
            break;

        case SK_WS_AUDIO_CMD_RESUME:
            SK_LOGI(TAG, "Processing RESUME command (not implemented)");
            break;

        default:
            SK_LOGW(TAG, "Unknown control command: 0x%02x", cmd);
            break;
    }
}

// 删除复杂的序列号验证函数 - 不再需要

// 删除内部函数 - 现在直接在控制命令处理中处理

void SkAudioStreamStart() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        SK_LOGE(TAG, "Audio stream not initialized");
        return;
    }

    // 无锁启动
    ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
    atomic_store(&ctrl->expectedSeq, 0);
    atomic_store(&ctrl->totalFrames, 0);

    SK_LOGI(TAG, "Audio streaming started (lock-free)");
}

void SkAudioStreamStop() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        return;
    }

    // 无锁停止
    ctrl->state = SK_AUDIO_STREAM_STATE_IDLE;

    uint32_t totalFrames = atomic_load(&ctrl->totalFrames);
    uint32_t lostFrames = atomic_load(&ctrl->lostFrames);

    SK_LOGI(TAG, "Audio streaming stopped (lock-free) - total_frames=%d, lost_frames=%d",
            totalFrames, lostFrames);
}

void SkAudioStreamPause() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        return;
    }

    // 无锁暂停（简化实现）
    if (ctrl->state == SK_AUDIO_STREAM_STATE_STREAMING) {
        ctrl->state = SK_AUDIO_STREAM_STATE_PAUSED;
        SK_LOGI(TAG, "Audio streaming paused (lock-free)");
    }
}

void SkAudioStreamResume() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        return;
    }

    // 无锁恢复（简化实现）
    if (ctrl->state == SK_AUDIO_STREAM_STATE_PAUSED) {
        ctrl->state = SK_AUDIO_STREAM_STATE_STREAMING;
        SK_LOGI(TAG, "Audio streaming resumed (lock-free)");
    }
}

SkAudioStreamState_e SkAudioStreamGetState() {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (ctrl->opusDecoder == NULL) {
        return SK_AUDIO_STREAM_STATE_IDLE;
    }

    return ctrl->state;
}

bool SkAudioStreamIsActive() {
    SkAudioStreamState_e state = SkAudioStreamGetState();
    return (state == SK_AUDIO_STREAM_STATE_STREAMING ||
            state == SK_AUDIO_STREAM_STATE_PAUSED);
}

void SkAudioStreamGetStats(SkAudioStreamStats_t *stats) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    if (stats == NULL || ctrl->opusDecoder == NULL) {
        return;
    }

    // 无锁读取原子统计信息
    memset(stats, 0, sizeof(SkAudioStreamStats_t));
    stats->totalFrames = atomic_load(&ctrl->totalFrames);
    stats->lostFrames = atomic_load(&ctrl->lostFrames);
    stats->bufferUnderrun = atomic_load(&ctrl->bufferUnderrun);
    stats->outOfOrderFrames = 0;  // 简化实现，不再跟踪
}

size_t SkAudioStreamFeedPlayAudio(uint16_t *buff, size_t len,
                                  SkAudioDownlinkTimeRecord *timeRecord) {
    SkAudioStreamCtrl_t *ctrl = &g_audioStreamCtrl;

    SK_LOGI(TAG, "FeedPlayAudio called: len=%d, state=%d", len, ctrl->state);

    // 检查初始化状态
    if (ctrl->opusDecoder == NULL) {
        SK_LOGI(TAG, "Audio stream not initialized, using original Opus decoder");
        return SkOpusDecFeedPlayAudio(buff, len, timeRecord);
    }

    // 无锁检查流状态
    if (ctrl->state != SK_AUDIO_STREAM_STATE_STREAMING) {
        SK_LOGI(TAG, "Not in streaming state (%d), using original Opus decoder", ctrl->state);
        return SkOpusDecFeedPlayAudio(buff, len, timeRecord);
    }

    SK_LOGI(TAG, "In streaming mode, getting audio from Opus decoder");
    // 在流状态下，直接使用Opus解码器的输出
    size_t result = SkOpusDecFeedPlayAudio(buff, len, timeRecord);

    // 原子更新统计信息
    if (result == 0) {
        atomic_fetch_add(&ctrl->bufferUnderrun, 1);
    }

    if (result > 0) {
        SK_LOGI(TAG, "Audio output: %d bytes to speaker (streaming mode)", result);
    } else {
        SK_LOGI(TAG, "No audio data available for output (streaming mode)");
    }

    return result;
}