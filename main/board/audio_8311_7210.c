/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: audio_8311_7210.c
 * @description: 8311+7210组合的音频处理的驱动.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "driver/i2s_std.h"
#include <driver/i2s_tdm.h>
#include <esp_codec_dev.h>
#include <esp_codec_dev_defaults.h>
#include <driver/i2c_master.h>
#include "soc/soc_caps.h"
#include "freertos/FreeRTOS.h"
#include <driver/gpio.h>
#include "esp_err.h"
#include "esp_log.h"
#include "sk_board_def.h"
#include "sk_board.h"
#include "sk_aec_board.h"
#include "sk_common.h"
#include "sk_os.h"

#define TAG "SkBsp"

#define CONFIG_SK_AUDIO_DEV_REG 0

#define ADC_I2S_CHANNEL 2
#define MAX_PLAY_SAMPLE_PER_CALL 800*2

typedef struct {
    bool micRef;
    bool micEnabled;
    bool spkEnabled;
    int micSampleRate;
    int spkSampleRate;
    int spkVol;
    int micVol;

    i2c_master_bus_handle_t i2cBus;

    const audio_codec_data_if_t* txDataIf;
    const audio_codec_data_if_t* rxDataIf;
    const audio_codec_ctrl_if_t* outCtrlIf;
    const audio_codec_if_t* outCodecIf;
    const audio_codec_ctrl_if_t* inCtrlIf;
    const audio_codec_if_t* inCodecIf;
    const audio_codec_gpio_if_t* gpioIf;

    esp_codec_dev_handle_t spkDev;
    esp_codec_dev_handle_t micDev;

    i2s_chan_handle_t txHandle;
    i2s_chan_handle_t rxHandle;
} SkAudioDev;

void AudioDevConfig(SkAudioDev *audioDev, i2c_master_bus_handle_t i2cBus, uint32_t sampleRate, int bitsPerChan);
void AudioDevInitI2SChannel(SkAudioDev *audioDev);
void AudioDevInitSpkI2S(SkAudioDev *audioDev);
void AudioDevInitMicI2S(SkAudioDev *audioDev);
int32_t AudioDevInitSpk(SkAudioDev *audioDev);
int32_t AudioDevInitMic(SkAudioDev *audioDev);
void AudioDevInitDataIf(SkAudioDev *audioDev);


SkAudioDev g_audioDev;

int32_t AudioDevInit(i2c_master_bus_handle_t i2cBus, uint32_t sampleRate, int bitsPerChan) {
    SkAudioDev *audioDev = &g_audioDev;

    AudioDevConfig(audioDev, i2cBus, sampleRate, sampleRate);
    AudioDevInitI2SChannel(audioDev);
    AudioDevInitSpkI2S(audioDev);
    AudioDevInitMicI2S(audioDev);
    AudioDevInitDataIf(audioDev);

    if (AudioDevInitSpk(audioDev) != 0) {
        ESP_LOGE(TAG, "Failed to initialize speaker device");
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "Spk init succ.");
    if (AudioDevInitMic(audioDev) != 0) {
        ESP_LOGE(TAG, "Failed to initialize microphone device");
        return SK_RET_FAIL;
    }
    ESP_LOGI(TAG, "Mic init succ.");
    ESP_LOGI(TAG, "Audio devices initialized");

    return SK_RET_SUCCESS;
}

void AudioDevDeinit() {
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
#if CONFIG_SPK_ENABLE
    ESP_ERROR_CHECK(esp_codec_dev_close(audioDev->spkDev));
    esp_codec_dev_delete(audioDev->spkDev);
#endif
#if CONFIG_MIC_ENABLE
    ESP_ERROR_CHECK(esp_codec_dev_close(audioDev->micDev));
    esp_codec_dev_delete(audioDev->micDev);
#endif
    audio_codec_delete_codec_if(audioDev->inCodecIf);
    audio_codec_delete_ctrl_if(audioDev->inCtrlIf);
    audio_codec_delete_codec_if(audioDev->outCodecIf);
    audio_codec_delete_ctrl_if(audioDev->outCtrlIf);
    audio_codec_delete_gpio_if(audioDev->gpioIf);
#if AUDIO_I2S_PORT_CNT == 1
    audio_codec_delete_data_if(audioDev->txDataIf);
    audioDev->rxDataIf = NULL;
#else
    audio_codec_delete_data_if(audioDev->txDataIf);
    audio_codec_delete_data_if(audioDev->rxDataIf);
#endif

    return;
}

void AudioDevSetSpkVol(int vol) {
#if CONFIG_SPK_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
    ESP_ERROR_CHECK(esp_codec_dev_set_out_vol(audioDev->spkDev, vol));
    audioDev->spkVol = vol;
    ESP_LOGI(TAG, "Set speaker volume to %d", vol);
#endif
    return;
}

void AudioDevSetMicVol(int vol) {
#if CONFIG_MIC_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
    ESP_ERROR_CHECK(esp_codec_dev_set_in_gain(audioDev->micDev, 24.0 + audioDev->micVol));
    ESP_LOGI(TAG, "Set microphone volume to %ddB", (24 + audioDev->micVol));
    audioDev->micVol = vol;
#endif
    return;
}

void AudioDevEnableMic(bool enable) {
#if CONFIG_MIC_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
    uint8_t i2sChannelMask; // 用于主芯片侧的I2S通道选择: 0: MIC1, 1: MIC3, 2: MIC2, 3: MIC4
    uint8_t devChannelMask; // 用于配置通道的增益: 0-3对应MIC1-MIC4

    if (enable == audioDev->micEnabled) {
        return;
    }
    if (enable) {
#if (CONFIG_BOARD_MIC == CONFIG_7210_MIC2)
        devChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(1);
        i2sChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(2);
#elif (CONFIG_BOARD_MIC == CONFIG_7210_MIC3)
        devChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(2);
        i2sChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(1);
#elif (CONFIG_BOARD_MIC == CONFIG_7210_MIC4)
        devChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(3);
        i2sChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(3);
#else
        devChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(0);
        i2sChannelMask = ESP_CODEC_DEV_MAKE_CHANNEL_MASK(0);
#endif
        if (audioDev->micRef) {
#if (CONFIG_REF_MIC == CONFIG_7210_MIC3)
        i2sChannelMask |= ESP_CODEC_DEV_MAKE_CHANNEL_MASK(1);
#elif (CONFIG_REF_MIC == CONFIG_7210_MIC2)
        i2sChannelMask |= ESP_CODEC_DEV_MAKE_CHANNEL_MASK(2);
#elif (CONFIG_REF_MIC == CONFIG_7210_MIC4)
        i2sChannelMask |= ESP_CODEC_DEV_MAKE_CHANNEL_MASK(3);
#endif
        }
        esp_codec_dev_sample_info_t fs = {
            .bits_per_sample = 16,
            .channel = 4,
            .channel_mask = i2sChannelMask,
            .sample_rate = (uint32_t)audioDev->micSampleRate,
            .mclk_multiple = 0,
        };
        ESP_ERROR_CHECK(esp_codec_dev_open(audioDev->micDev, &fs));
        ESP_ERROR_CHECK(esp_codec_dev_set_in_channel_gain(audioDev->micDev, devChannelMask, 40.0));
    } else {
        ESP_ERROR_CHECK(esp_codec_dev_close(audioDev->micDev));
    }
    audioDev->micEnabled = enable;
    ESP_LOGI(TAG, "Mic %s", enable ? "enabled" : "disabled");
#if CONFIG_SK_AUDIO_DEV_REG    
    audioDev->inCodecIf->dump_reg(audioDev->inCodecIf);
#endif
#endif
    return;
}

void AudioDevEnableSpk(bool enable) {
#if CONFIG_SPK_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;

    if (enable == audioDev->spkEnabled) {
        return;
    }
    if (enable) {
        // Play 16bit 1 channel
        esp_codec_dev_sample_info_t fs = {
            .bits_per_sample = 16,
            .channel = 1,
            .channel_mask = 0,
            .sample_rate = (uint32_t)audioDev->spkSampleRate,
            .mclk_multiple = 0,
        };
        ESP_ERROR_CHECK(esp_codec_dev_open(audioDev->spkDev, &fs));
        ESP_ERROR_CHECK(esp_codec_dev_set_out_vol(audioDev->spkDev, audioDev->spkVol));
    } else {
        ESP_ERROR_CHECK(esp_codec_dev_close(audioDev->spkDev));
    }
    audioDev->spkEnabled = enable;
    ESP_LOGI(TAG, "Spk %s", enable ? "enabled" : "disabled");
#if CONFIG_SK_AUDIO_DEV_REG    
    audioDev->outCodecIf->dump_reg(audioDev->outCodecIf);
#endif
#endif
    return;
}

int AudioDevRead(int16_t* dest, int bufSize) {
#if CONFIG_MIC_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;
    if (audioDev->micEnabled) {
        ESP_ERROR_CHECK_WITHOUT_ABORT(esp_codec_dev_read(audioDev->micDev, (void*)dest, bufSize));
    } else {
        memset(dest, 0, bufSize);
    }
#else
    memset(dest, 0, bufSize);
    vTaskDelay(30);
#endif
    return bufSize;
}

int AudioDevWrite(const int16_t* data, int dataLen) {
#if CONFIG_SPK_ENABLE
    SkAudioDev *audioDev = (SkAudioDev *)&g_audioDev;

    if (audioDev->spkEnabled) {
        ESP_ERROR_CHECK_WITHOUT_ABORT(esp_codec_dev_write(audioDev->spkDev, (void*)data, dataLen));
    }
    return ESP_OK;
#else
    vTaskDelay(30);
    return ESP_OK;
#endif
}

int32_t AudioDevGetSpkVol() {
    return g_audioDev.spkVol;
}

int32_t AudioDevGetMicVol() {
    return g_audioDev.micVol;
}


void AudioDevConfig(SkAudioDev *audioDev, i2c_master_bus_handle_t i2cBus, uint32_t sampleRate, int bitsPerChan) {
    audioDev->i2cBus = i2cBus;
    audioDev->txHandle = NULL;
    audioDev->rxHandle = NULL;
    audioDev->micSampleRate = sampleRate;
    audioDev->spkSampleRate = sampleRate;
    audioDev->spkVol = 70;
    audioDev->micVol = 6;
    audioDev->micRef = true;

    return;
}

void AudioDevInitSpkI2S(SkAudioDev *audioDev) {
    i2s_std_config_t outStdCfg = {
        .clk_cfg = {
            .sample_rate_hz = (uint32_t)audioDev->spkSampleRate,
            .clk_src = I2S_CLK_SRC_DEFAULT,
            .ext_clk_freq_hz = 0,
            .mclk_multiple = I2S_MCLK_MULTIPLE_256
        },
        .slot_cfg = {
            .data_bit_width = I2S_DATA_BIT_WIDTH_16BIT,
            .slot_bit_width = I2S_SLOT_BIT_WIDTH_AUTO,
            .slot_mode = I2S_SLOT_MODE_MONO,
            .slot_mask = I2S_STD_SLOT_LEFT,
            .ws_width = I2S_DATA_BIT_WIDTH_16BIT,
            .ws_pol = false,
            .bit_shift = true,
            .left_align = true,
            .big_endian = false,
            .bit_order_lsb = false
        },
        .gpio_cfg = {
            .mclk = SPK_I2S_GPIO_MCLK,
            .bclk = SPK_I2S_GPIO_BCLK,
            .ws = SPK_I2S_GPIO_WS,
            .dout = SPK_I2S_GPIO_DOUT,
            .din = I2S_GPIO_UNUSED,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false
            }
        }
    };

    ESP_ERROR_CHECK(i2s_channel_init_std_mode(audioDev->txHandle, &outStdCfg));
    ESP_LOGI(TAG, "Simplex channels created");
    return;
}

void AudioDevInitMicI2S(SkAudioDev *audioDev) {
    i2s_tdm_config_t inTdmCfg = {
        .clk_cfg = {
            .sample_rate_hz = (uint32_t)audioDev->micSampleRate,
            .clk_src = I2S_CLK_SRC_DEFAULT,
            .ext_clk_freq_hz = 0,
            .mclk_multiple = I2S_MCLK_MULTIPLE_256,
            .bclk_div = 8,
        },
        .slot_cfg = {
            .data_bit_width = I2S_DATA_BIT_WIDTH_16BIT,
            .slot_bit_width = I2S_SLOT_BIT_WIDTH_AUTO,
            .slot_mode = I2S_SLOT_MODE_STEREO,
            .slot_mask = I2S_TDM_SLOT0 | I2S_TDM_SLOT1 | I2S_TDM_SLOT2 | I2S_TDM_SLOT3,
            .ws_width = I2S_TDM_AUTO_WS_WIDTH,
            .ws_pol = false,
            .bit_shift = true,
            .left_align = false,
            .big_endian = false,
            .bit_order_lsb = false,
            .skip_mask = false,
            .total_slot = I2S_TDM_AUTO_SLOT_NUM
        },
        .gpio_cfg = {
            .mclk = MIC_I2S_GPIO_MCLK,
            .bclk = MIC_I2S_GPIO_BCLK,
            .ws = MIC_I2S_GPIO_WS,
            .dout = I2S_GPIO_UNUSED,
            .din = MIC_I2S_GPIO_DIN,
            .invert_flags = {
                .mclk_inv = false,
                .bclk_inv = false,
                .ws_inv = false
            }
        }
    };

    ESP_ERROR_CHECK(i2s_channel_init_tdm_mode(audioDev->rxHandle, &inTdmCfg));
    ESP_LOGI(TAG, "Duplex channels created");
    return;
}

void AudioDevInitI2SChannel(SkAudioDev *audioDev) {
#if AUDIO_I2S_PORT_CNT == 1
    i2s_chan_config_t chanCfg = {
        .id = I2S_NUM_0,
        .role = I2S_ROLE_MASTER,
        .dma_desc_num = 8,
        .dma_frame_num = 240,
        .auto_clear_after_cb = true,
        .auto_clear_before_cb = false,
        .intr_priority = 0,
    };
    ESP_ERROR_CHECK(i2s_new_channel(&chanCfg, &audioDev->txHandle, &audioDev->rxHandle));
#else
    i2s_chan_config_t chanCfg = {
        .id = I2S_NUM_0,
        .role = I2S_ROLE_MASTER,
        .dma_desc_num = 4,
        .dma_frame_num = 240,
        .auto_clear_after_cb = true,
        .auto_clear_before_cb = false,
        .intr_priority = 0,
    };
    ESP_ERROR_CHECK(i2s_new_channel(&chanCfg, &audioDev->txHandle, NULL));
    chanCfg.id = I2S_NUM_1;
    ESP_ERROR_CHECK(i2s_new_channel(&chanCfg, NULL, &audioDev->rxHandle));
#endif
    return;
}

void AudioDevInitDataIf(SkAudioDev *audioDev) {
#if AUDIO_I2S_PORT_CNT == 1
    // Do initialize of related interface: data_if, ctrl_if and gpio_if
    audio_codec_i2s_cfg_t i2sCfg = {
        .port = I2S_NUM_0,
        .rx_handle = audioDev->rxHandle,
        .tx_handle = audioDev->txHandle,
    };
    audioDev->txDataIf = audio_codec_new_i2s_data(&i2sCfg);
    if (audioDev->txDataIf == NULL) {
        ESP_LOGE(TAG, "Failed to create I2S data interface");
        return;
    }
    audioDev->rxDataIf = audioDev->txDataIf;
#else
    // Do initialize of related interface: data_if, ctrl_if and gpio_if
    audio_codec_i2s_cfg_t i2sCfgTx = {
        .port = I2S_NUM_0,
        .rx_handle = NULL,
        .tx_handle = audioDev->txHandle,
    };
    audioDev->txDataIf = audio_codec_new_i2s_data(&i2sCfgTx);
    if (audioDev->txDataIf == NULL) {
        ESP_LOGE(TAG, "Failed to create I2S data interface");
        return;
    }
    // Do initialize of related interface: data_if, ctrl_if and gpio_if
    audio_codec_i2s_cfg_t i2sCfgRx = {
        .port = I2S_NUM_1,
        .rx_handle = audioDev->rxHandle,
        .tx_handle = NULL,
    };
    audioDev->rxDataIf = audio_codec_new_i2s_data(&i2sCfgRx);
    if (audioDev->rxDataIf == NULL) {
        ESP_LOGE(TAG, "Failed to create I2S data interface");
        return;
    }
#endif
}

int32_t AudioDevInitSpk(SkAudioDev *audioDev) {
#if CONFIG_SPK_ENABLE
    audio_codec_i2c_cfg_t i2cCfg = {
        .port = (i2c_port_t)1,
        .addr = AUDIO_CODEC_ES8311_ADDR,
        .bus_handle = audioDev->i2cBus,
    };
    audioDev->outCtrlIf = audio_codec_new_i2c_ctrl(&i2cCfg);
    if (audioDev->outCtrlIf == NULL) {
        ESP_LOGE(TAG, "Failed to create I2C control interface");
        return -1;
    }

    audioDev->gpioIf = audio_codec_new_gpio();
    if (audioDev->gpioIf == NULL) {
        ESP_LOGE(TAG, "Failed to create GPIO interface");
        return -1;
    }

    es8311_codec_cfg_t es8311Cfg = {};
    es8311Cfg.ctrl_if = audioDev->outCtrlIf;
    es8311Cfg.gpio_if = audioDev->gpioIf;
    es8311Cfg.codec_mode = ESP_CODEC_DEV_WORK_MODE_DAC;
    es8311Cfg.pa_pin = AUDIO_CODEC_PA_PIN;
    es8311Cfg.use_mclk = true;
#if (CONFIG_BOARD_TYPE == CONFIG_SK_BOARD_BOX3)
    es8311Cfg.hw_gain.pa_voltage = 5.0;
#else
    es8311Cfg.hw_gain.pa_voltage = 3.3;
#endif    
    es8311Cfg.hw_gain.codec_dac_voltage = 3.3;
    audioDev->outCodecIf = es8311_codec_new(&es8311Cfg);
    if (audioDev->outCodecIf == NULL) {
        ESP_LOGE(TAG, "Failed to create ES8311 codec");
        return -1;
    }

    esp_codec_dev_cfg_t devCfg = {
        .dev_type = ESP_CODEC_DEV_TYPE_OUT,
        .codec_if = audioDev->outCodecIf,
        .data_if = audioDev->txDataIf,
    };
    audioDev->spkDev = esp_codec_dev_new(&devCfg);
    if (audioDev->spkDev == NULL) {
        ESP_LOGE(TAG, "Failed to create speaker device");
        return -1;
    }
#else
    audioDev->spkDev = NULL;
#endif
    return 0;
}

int32_t AudioDevInitMic(SkAudioDev *audioDev) {
#if CONFIG_MIC_ENABLE
    audio_codec_i2c_cfg_t i2cCfg = {
        .port = (i2c_port_t)1,
        .addr = AUDIO_CODEC_ES7210_ADDR,
        .bus_handle = audioDev->i2cBus,
    };
    audioDev->inCtrlIf = audio_codec_new_i2c_ctrl(&i2cCfg);
    if (audioDev->inCtrlIf == NULL) {
        ESP_LOGE(TAG, "Failed to create I2C control interface");
        return -1;
    }

    es7210_codec_cfg_t es7210Cfg = {};
    es7210Cfg.ctrl_if = audioDev->inCtrlIf;
    es7210Cfg.mic_selected = ES7120_SEL_MIC1 | ES7120_SEL_MIC2 | ES7120_SEL_MIC3 | ES7120_SEL_MIC4;
    audioDev->inCodecIf = es7210_codec_new(&es7210Cfg);
    if (audioDev->inCodecIf == NULL) {
        ESP_LOGE(TAG, "Failed to create ES7210 codec");
        return -1;
    }

    esp_codec_dev_cfg_t devCfg = {
        .dev_type = ESP_CODEC_DEV_TYPE_IN,
        .codec_if = audioDev->inCodecIf,
        .data_if = audioDev->rxDataIf,
    };

    audioDev->micDev = esp_codec_dev_new(&devCfg);
    if (audioDev->micDev == NULL) {
        ESP_LOGE(TAG, "Failed to create microphone device");
        return -1;
    }
#else
    audioDev->micDev = NULL;
#endif
    return 0;
}