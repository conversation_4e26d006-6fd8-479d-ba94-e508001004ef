/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio_stream.h
 * @description: WebSocket音频流处理接口定义
 * @author: System
 * @date: 2025-07-29
 */

#ifndef SK_AUDIO_STREAM_H
#define SK_AUDIO_STREAM_H

#include <stdint.h>
#include <stdbool.h>
#include "sk_common.h"
#include "sk_audio.h"

#ifdef __cplusplus
extern "C" {
#endif

// 音频流状态定义
typedef enum {
    SK_AUDIO_STREAM_STATE_IDLE = 0,
    SK_AUDIO_STREAM_STATE_STREAMING,
    SK_AUDIO_STREAM_STATE_PAUSED,
} SkAudioStreamState_e;

// 音频流统计信息
typedef struct {
    uint32_t totalFrames;       // 总接收帧数
    uint32_t lostFrames;        // 丢失帧数
    uint32_t outOfOrderFrames;  // 乱序帧数
    uint32_t bufferUnderrun;    // 缓冲区下溢次数
    uint32_t bufferOverrun;     // 缓冲区上溢次数
} SkAudioStreamStats_t;

// 初始化和反初始化
int32_t SkAudioStreamInit();
void SkAudioStreamDeinit();

// 音频流控制
void SkAudioStreamStart();
void SkAudioStreamStop();
void SkAudioStreamPause();
void SkAudioStreamResume();

// 状态查询
SkAudioStreamState_e SkAudioStreamGetState();
bool SkAudioStreamIsActive();
void SkAudioStreamGetStats(SkAudioStreamStats_t *stats);

// WebSocket数据处理回调（供WebSocket模块调用）
void SkAudioStreamOnBinaryData(void *arg, void *data, uint16_t len);

// 音频播放回调函数（供SkPlayer使用）
size_t SkAudioStreamFeedPlayAudio(uint16_t *buff, size_t len, SkAudioDownlinkTimeRecord *timeRecord);

#ifdef __cplusplus
}
#endif

#endif // SK_AUDIO_STREAM_H