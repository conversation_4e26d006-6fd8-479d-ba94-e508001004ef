# WebSocket音频流系统故障排查指南

## 问题1：网络栈断言错误

### 错误现象
```
assert failed: tcpip_send_msg_wait_sem /IDF/components/lwip/lwip/src/api/tcpip.c:454 (Invalid mbox)
```

### 原因分析
- 在网络栈未完全初始化时尝试建立WebSocket连接
- WiFi连接未建立就尝试网络操作

### 解决方案 ✅
1. **不要在系统初始化时立即连接WebSocket**
2. **WebSocket模块自动监听WiFi事件，在WiFi连接成功后自动启动连接**

修改后的代码流程：
```c
// main.c - 系统初始化时只设置服务器IP，不立即连接
SkWsInit();          // 初始化WebSocket并注册WiFi事件回调
SkWsStart();         // 启动WebSocket任务
SkWsSetServerIp("************", 8766);  // 设置服务器地址
// 注意：不需要手动调用 SkWsStartConnect()

// sk_websocket.c - WiFi事件回调自动处理连接
static void SkWsOnWifiEvent(uint32_t event) {
    switch (event) {
        case SK_WIFI_EVENT_STA_CONNECTED:
            // WiFi连接成功，自动启动WebSocket连接
            vTaskDelay(pdMS_TO_TICKS(1000));  // 等待网络栈准备好
            SkWsStartConnect();
            break;
        case SK_WIFI_EVENT_STA_STOP:
            // WiFi断开，停止WebSocket连接
            SkWsStopConnect();
            break;
    }
}
```

## 问题2：服务器连接失败

### 错误现象
- ESP32无法连接到音频流服务器
- WebSocket连接超时

### 排查步骤

#### 1. 检查服务器是否运行
```bash
cd tools
./start_audio_server.sh --host 0.0.0.0 --port 8766
```

#### 2. 测试服务器连接
```bash
cd tools
python3 test_server_connection.py
```

#### 3. 检查网络配置
- 确保ESP32和服务器在同一网络
- 检查IP地址是否正确
- 检查端口是否被占用

#### 4. 检查防火墙设置
```bash
# Linux/macOS
sudo ufw allow 8766
# 或者临时关闭防火墙测试
sudo ufw disable

# Windows
# 在Windows防火墙中允许端口8766
```

## 问题3：音频播放问题

### 错误现象
- 收到音频数据但没有声音
- 音频断续或有杂音

### 排查步骤

#### 1. 检查音频流状态
在ESP32端查看日志：
```
I (xxxx) AudioStream: Audio streaming started
I (xxxx) AudioStream: Processed audio frame: seq=0, size=xxx
```

#### 2. 检查Opus解码器状态
```
I (xxxx) OpusDec: Opus decoder initialized
I (xxxx) OpusDec: Decoding frame: seq=xxx
```

#### 3. 检查音频播放器状态
```
I (xxxx) Player: Audio player started
I (xxxx) Player: Playing audio data: xxx bytes
```

## 问题4：服务器端问题

### 错误现象
- 服务器启动失败
- 音频文件编码错误

### 排查步骤

#### 1. 检查Python依赖
```bash
cd tools
pip install -r requirements.txt
```

#### 2. 检查音频文件格式
- 确保音频文件是WAV格式
- 采样率：16kHz
- 位深：16位
- 声道：单声道

#### 3. 生成测试音频
```bash
cd tools
python3 generate_test_audio.py --output test.wav --duration 10
```

## 调试技巧

### 1. 启用详细日志
在ESP32代码中设置日志级别：
```c
esp_log_level_set("AudioStream", ESP_LOG_DEBUG);
esp_log_level_set("SkWs", ESP_LOG_DEBUG);
```

### 2. 监控网络流量
```bash
# 使用tcpdump监控WebSocket流量
sudo tcpdump -i any -w audio_stream.pcap port 8766
```

### 3. 检查内存使用
```c
// 在ESP32代码中添加内存监控
SK_OS_MODULE_MEM_STAT("AudioStream", true);
```

### 4. 查看WebSocket连接状态
```c
bool connected = SkWsIsConnected();
SK_LOGI("Debug", "WebSocket connected: %s", connected ? "Yes" : "No");
```

## 常见问题FAQ

### Q1: ESP32重启循环
**A**: 通常是内存不足或栈溢出，检查任务栈大小和内存分配

### Q2: 音频延迟很大
**A**: 调整音频缓冲区大小和网络发送间隔

### Q3: 连接经常断开
**A**: 检查网络稳定性，实现重连机制

### Q4: 音频质量差
**A**: 检查Opus编码参数和网络带宽

## 成功运行的标志

### ESP32端日志
```
I (xxxx) SmTop: WiFi connected, starting WebSocket connection
I (xxxx) SkWs: websocket->eventGroup create success!
I (xxxx) SkWs: WebSocket connected successfully
I (xxxx) AudioStream: Audio stream initialized successfully
I (xxxx) AudioStream: Audio streaming started
I (xxxx) AudioStream: Processed audio frame: seq=0, size=xxx
```

### 服务器端日志
```
Audio stream server starting on ws://0.0.0.0:8766
Client connected: ('192.168.3.xxx', xxxxx)
Starting audio stream: test.wav
Audio file encoded: xxx frames
Sent frame 1/xxx, size: xxx bytes
```

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误日志
2. 网络配置信息
3. 音频文件信息
4. ESP32和服务器的IP地址
