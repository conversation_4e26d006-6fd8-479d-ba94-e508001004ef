#!/usr/bin/env python3
"""
音频流系统测试脚本
"""

import asyncio
import websockets
import struct
import time
from pathlib import Path

async def test_websocket_connection():
    """测试WebSocket连接"""
    uri = "ws://localhost:8765"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("WebSocket连接成功")
            
            # 发送测试消息
            test_message = "Hello from test client"
            await websocket.send(test_message)
            print(f"发送测试消息: {test_message}")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"收到响应: {response}")
            except asyncio.TimeoutError:
                print("等待响应超时")
                
    except Exception as e:
        print(f"连接失败: {e}")

def create_test_audio_packet(seq_num, data):
    """创建测试音频数据包"""
    version = 0x01
    data_type = 0x01  # SK_WS_DATA_TYPE_AUDIO
    payload_len = len(data)
    reserved = 0
    
    # 打包头部（小端序）
    header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
    return header + data

def create_test_control_packet(cmd):
    """创建测试控制数据包"""
    version = 0x01
    data_type = 0x02  # SK_WS_DATA_TYPE_AUDIO_CONTROL
    seq_num = 0
    payload_len = 1
    reserved = 0
    
    header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
    return header + bytes([cmd])

async def test_audio_protocol():
    """测试音频协议"""
    uri = "ws://localhost:8765"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("开始测试音频协议...")
            
            # 发送开始命令
            start_cmd = create_test_control_packet(0x01)  # START
            await websocket.send(start_cmd)
            print("发送开始命令")
            
            # 发送几个测试音频帧
            for i in range(5):
                # 创建测试音频数据（简单的正弦波）
                test_data = bytes([i % 256] * 100)  # 100字节测试数据
                audio_packet = create_test_audio_packet(i, test_data)
                
                await websocket.send(audio_packet)
                print(f"发送音频帧 {i}")
                
                await asyncio.sleep(0.06)  # 60ms间隔
            
            # 发送停止命令
            stop_cmd = create_test_control_packet(0x02)  # STOP
            await websocket.send(stop_cmd)
            print("发送停止命令")
            
    except Exception as e:
        print(f"协议测试失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Audio Stream Test Client')
    parser.add_argument('--test', choices=['connection', 'protocol'], 
                       default='connection', help='Test type')
    
    args = parser.parse_args()
    
    if args.test == 'connection':
        print("测试WebSocket连接...")
        asyncio.run(test_websocket_connection())
    elif args.test == 'protocol':
        print("测试音频协议...")
        asyncio.run(test_audio_protocol())

if __name__ == "__main__":
    main()
