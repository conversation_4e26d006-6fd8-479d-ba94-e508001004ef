#!/bin/bash

# WebSocket音频流服务器启动脚本

# 检查Python版本
python3 --version > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: Python 3 is required"
    exit 1
fi

# 检查并安装依赖
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

source venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt

echo "Starting audio stream server..."
python3 audio_stream_server.py "$@"
