#!/usr/bin/env python3
"""
WebSocket音频流服务器
支持将音频文件实时流式传输到ESP32终端
"""

import asyncio
import websockets
import struct
import time
import wave
import threading
from pathlib import Path
from typing import Set, Optional, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

try:
    import opuslib
    OPUS_AVAILABLE = True
except ImportError:
    logger.warning("opuslib not available, will use PCM data directly")
    OPUS_AVAILABLE = False

class OpusAudioStreamer:
    """Opus音频流处理器"""
    
    def __init__(self, sample_rate=16000, channels=1, frame_duration=60):
        self.sample_rate = sample_rate
        self.channels = channels
        self.frame_duration = frame_duration  # ms
        self.frame_size = sample_rate * frame_duration // 1000
        self.seq_num = 0
        
        if OPUS_AVAILABLE:
            self.encoder = opuslib.Encoder(
                sample_rate, 
                channels, 
                opuslib.APPLICATION_AUDIO
            )
        else:
            self.encoder = None
            logger.warning("Opus encoder not available")
    
    def encode_audio_file(self, file_path: Path) -> List[bytes]:
        """将音频文件编码为Opus帧序列"""
        frames = []
        
        try:
            with wave.open(str(file_path), 'rb') as wav_file:
                # 检查音频格式
                sample_width = wav_file.getsampwidth()
                frame_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                
                logger.info(f"Audio file info: {sample_width*8}bit, {frame_rate}Hz, {channels}ch")
                
                # 验证格式
                if sample_width != 2:
                    raise ValueError(f"Unsupported sample width: {sample_width} (need 16-bit)")
                if frame_rate != self.sample_rate:
                    raise ValueError(f"Unsupported sample rate: {frame_rate} (need {self.sample_rate}Hz)")
                if channels != self.channels:
                    raise ValueError(f"Unsupported channels: {channels} (need {self.channels})")
                
                # 读取并编码音频数据
                frame_count = 0
                while True:
                    pcm_data = wav_file.readframes(self.frame_size)
                    if len(pcm_data) < self.frame_size * 2:  # 16-bit = 2 bytes
                        if len(pcm_data) > 0:
                            # 填充最后一帧
                            pcm_data += b'\x00' * (self.frame_size * 2 - len(pcm_data))
                        else:
                            break
                    
                    if self.encoder:
                        # Opus编码
                        opus_data = self.encoder.encode(pcm_data, self.frame_size)
                    else:
                        # 直接使用PCM数据（用于测试）
                        opus_data = pcm_data
                    
                    frames.append(opus_data)
                    frame_count += 1
                
                logger.info(f"Encoded {frame_count} frames from {file_path}")
                
        except Exception as e:
            logger.error(f"Failed to encode audio file {file_path}: {e}")
            raise
        
        return frames
    
    def create_audio_packet(self, opus_data: bytes) -> bytes:
        """创建音频数据包"""
        # SkWsBinaryHeader_t结构
        version = 0x01
        data_type = 0x01  # SK_WS_DATA_TYPE_AUDIO
        seq_num = self.seq_num
        payload_len = len(opus_data)
        reserved = 0
        
        # 打包头部（小端序）
        header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
        
        self.seq_num = (self.seq_num + 1) & 0xFFFF
        return header + opus_data
    
    def reset_sequence(self):
        """重置序列号"""
        self.seq_num = 0

class AudioStreamServer:
    """音频流WebSocket服务器"""
    
    def __init__(self, host='0.0.0.0', port=8765):
        self.host = host
        self.port = port
        self.streamer = OpusAudioStreamer()
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.streaming = False
        self.stream_thread: Optional[threading.Thread] = None
        
    async def handle_client(self, websocket, path):
        """处理客户端连接"""
        self.clients.add(websocket)
        client_addr = websocket.remote_address
        logger.info(f"Client connected: {client_addr}")
        
        try:
            await websocket.wait_closed()
        except Exception as e:
            logger.error(f"Client {client_addr} error: {e}")
        finally:
            self.clients.discard(websocket)
            logger.info(f"Client disconnected: {client_addr}")
    
    async def stream_audio_file(self, file_path: Path):
        """流式发送音频文件"""
        if not self.clients:
            logger.warning("No connected clients")
            return
        
        if self.streaming:
            logger.warning("Already streaming")
            return
        
        logger.info(f"Starting audio stream: {file_path}")
        
        try:
            # 重置序列号
            self.streamer.reset_sequence()
            
            # 编码音频文件
            opus_frames = self.streamer.encode_audio_file(file_path)
            if not opus_frames:
                logger.error("No audio frames to stream")
                return
            
            logger.info(f"Audio file encoded: {len(opus_frames)} frames")
            
            # 发送开始命令
            start_cmd = self.create_control_packet(0x01)  # START
            await self.broadcast(start_cmd)
            await asyncio.sleep(0.1)  # 等待客户端准备
            
            self.streaming = True
            
            # 按照音频播放速率发送帧
            frame_interval = self.streamer.frame_duration / 1000.0  # 转换为秒
            start_time = time.time()
            
            for i, opus_frame in enumerate(opus_frames):
                if not self.streaming or not self.clients:
                    break
                
                # 创建音频数据包
                packet = self.streamer.create_audio_packet(opus_frame)
                
                # 广播到所有客户端
                await self.broadcast(packet)
                
                if (i + 1) % 10 == 0:  # 每10帧打印一次
                    logger.info(f"Sent frame {i+1}/{len(opus_frames)}, size: {len(opus_frame)} bytes")
                
                # 控制发送速率
                expected_time = start_time + (i + 1) * frame_interval
                current_time = time.time()
                sleep_time = expected_time - current_time
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                elif sleep_time < -0.1:  # 如果延迟超过100ms，记录警告
                    logger.warning(f"Streaming behind schedule by {-sleep_time:.3f}s")
            
            # 发送停止命令
            stop_cmd = self.create_control_packet(0x02)  # STOP
            await self.broadcast(stop_cmd)
            
            logger.info("Audio stream completed")
            
        except Exception as e:
            logger.error(f"Error during streaming: {e}")
        finally:
            self.streaming = False
    
    def create_control_packet(self, cmd: int) -> bytes:
        """创建控制数据包"""
        version = 0x01
        data_type = 0x02  # SK_WS_DATA_TYPE_AUDIO_CONTROL
        seq_num = 0
        payload_len = 1
        reserved = 0
        
        header = struct.pack('<BBHHH', version, data_type, seq_num, payload_len, reserved)
        return header + bytes([cmd])
    
    async def broadcast(self, data: bytes):
        """广播数据到所有客户端"""
        if not self.clients:
            return
        
        # 并发发送到所有客户端
        results = await asyncio.gather(
            *[self.send_to_client(client, data) for client in self.clients.copy()],
            return_exceptions=True
        )
        
        # 检查发送结果
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"Failed to send to client: {result}")
    
    async def send_to_client(self, client, data: bytes):
        """发送数据到单个客户端"""
        try:
            await client.send(data)
        except Exception as e:
            # 客户端断开连接，从列表中移除
            self.clients.discard(client)
            raise e
    
    def stop_streaming(self):
        """停止流式传输"""
        self.streaming = False

    async def start_server(self):
        """启动服务器"""
        logger.info(f"Audio stream server starting on ws://{self.host}:{self.port}")

        async with websockets.serve(self.handle_client, self.host, self.port):
            logger.info("Server started, waiting for connections...")

            # 命令行交互循环
            loop = asyncio.get_event_loop()

            while True:
                try:
                    # 在单独线程中获取用户输入
                    user_input = await loop.run_in_executor(
                        None,
                        lambda: input("Enter audio file path (or 'quit' to exit): ").strip()
                    )

                    if user_input.lower() in ['quit', 'exit', 'q']:
                        break

                    if user_input.lower() == 'stop':
                        self.stop_streaming()
                        continue

                    file_path = Path(user_input)
                    if not file_path.exists():
                        logger.error(f"File not found: {file_path}")
                        continue

                    if not file_path.suffix.lower() == '.wav':
                        logger.error("Only WAV files are supported")
                        continue

                    # 开始流式传输
                    await self.stream_audio_file(file_path)

                except KeyboardInterrupt:
                    break
                except Exception as e:
                    logger.error(f"Error: {e}")

            logger.info("Server shutting down...")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='WebSocket Audio Stream Server')
    parser.add_argument('--host', default='0.0.0.0', help='Server host')
    parser.add_argument('--port', type=int, default=8765, help='Server port')
    parser.add_argument('--sample-rate', type=int, default=16000, help='Audio sample rate')
    parser.add_argument('--channels', type=int, default=1, help='Audio channels')
    parser.add_argument('--frame-duration', type=int, default=60, help='Frame duration in ms')

    args = parser.parse_args()

    # 创建服务器
    server = AudioStreamServer(args.host, args.port)
    server.streamer = OpusAudioStreamer(
        args.sample_rate,
        args.channels,
        args.frame_duration
    )

    # 运行服务器
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("Server stopped by user")

if __name__ == "__main__":
    main()
