#!/usr/bin/env python3
"""
生成测试音频文件
"""

import wave
import numpy as np
import argparse

def generate_sine_wave(frequency, duration, sample_rate=16000, amplitude=0.3):
    """生成正弦波"""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    wave_data = amplitude * np.sin(2 * np.pi * frequency * t)
    return (wave_data * 32767).astype(np.int16)

def generate_test_audio(filename, duration=10, sample_rate=16000):
    """生成测试音频文件"""
    # 生成不同频率的音调
    frequencies = [440, 523, 659, 784]  # A4, C5, E5, G5
    segment_duration = duration / len(frequencies)
    
    audio_data = []
    for freq in frequencies:
        segment = generate_sine_wave(freq, segment_duration, sample_rate)
        audio_data.append(segment)
    
    # 合并所有段
    full_audio = np.concatenate(audio_data)
    
    # 保存为WAV文件
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(full_audio.tobytes())
    
    print(f"Generated test audio: {filename}")
    print(f"Duration: {duration}s, Sample rate: {sample_rate}Hz, Channels: 1")

def main():
    parser = argparse.ArgumentParser(description='Generate test audio file')
    parser.add_argument('--output', '-o', default='test_audio.wav', help='Output filename')
    parser.add_argument('--duration', '-d', type=float, default=10, help='Duration in seconds')
    parser.add_argument('--sample-rate', '-r', type=int, default=16000, help='Sample rate')
    
    args = parser.parse_args()
    
    generate_test_audio(args.output, args.duration, args.sample_rate)

if __name__ == "__main__":
    main()
